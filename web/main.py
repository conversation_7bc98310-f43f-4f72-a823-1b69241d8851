import asyncio
import logging
import traceback
import uuid
from pathlib import Path
from typing import List, Dict

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.responses import StreamingResponse, FileResponse

from config import config
from tp_utils.log_util import TLogger
from tp_utils.mongo_util import MONGO_UTIL
from .agent import agent_run
from .models import SSEMessage, StandardResponse

# 全局变量
logger = TLogger.get_logger(__name__)
uvicorn_error_logger = logging.getLogger("uvicorn.error")
uvicorn_access_logger = logging.getLogger("uvicorn.access")
# 清空 uvicorn 自带 handler
uvicorn_error_logger.handlers.clear()
uvicorn_access_logger.handlers.clear()
# logger 的所有 handler
for handler in logger.handlers:
    uvicorn_error_logger.addHandler(handler)
    uvicorn_access_logger.addHandler(handler)
uvicorn_error_logger.setLevel("DEBUG")
uvicorn_access_logger.setLevel("DEBUG")

# 启动web先存储llm配置，先这样实现
llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one({"flag": True, "delFlag": False})
if not llm_cfg:
    raise ValueError("No LLM config found")
llm_cfg_id = str(llm_cfg['_id'])
llm_config_id_path = Path(config['web']['llm_config_id_path'])
llm_config_id_path.write_text(llm_cfg_id)

app = FastAPI()


# ====== 全局异常处理器 ======
@app.exception_handler(Exception)
async def all_exception_handler(request: Request, exc: Exception):
    logger.error("Unhandled exception in request %s\n%s",
                 request.url.path, traceback.format_exc())
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal Server Error"}
    )


# 存储每个客户端的队列，key是客户端ID，value是队列
client_queues: Dict[str, asyncio.Queue] = {}

is_task_running = False
cur_dir = Path(__file__).parent

# 当前流式消息状态
current_reasoning_content = ""  # 当前正在进行的推理内容
current_content_content = ""  # 当前正在进行的回答内容
is_reasoning_streaming = False  # 是否正在流式输出推理
is_content_streaming = False  # 是否正在流式输出回答


async def broadcast_message(message: SSEMessage):
    """向所有连接的客户端广播消息"""
    global current_reasoning_content, current_content_content, is_reasoning_streaming, is_content_streaming

    # 更新当前流式状态并发送完整内容
    if message.type == "Reasoning" and message.is_partial:
        if not is_reasoning_streaming:
            current_reasoning_content = ""
            is_reasoning_streaming = True
        current_reasoning_content += message.content or ""
        # 发送完整内容而不是增量
        complete_message = SSEMessage(
            type="Reasoning",
            content=current_reasoning_content,
            is_partial=True
        )
    elif message.type == "ReasoningDone":
        is_reasoning_streaming = False
        complete_message = message
    elif message.type == "Content" and message.is_partial:
        if not is_content_streaming:
            current_content_content = ""
            is_content_streaming = True
        current_content_content += message.content or ""
        # 发送完整内容而不是增量
        complete_message = SSEMessage(
            type="Content",
            content=current_content_content,
            is_partial=True
        )
    elif message.type == "ContentDone":
        is_content_streaming = False
        complete_message = message
    elif message.type == "Start":
        # 重置流式状态
        current_reasoning_content = ""
        current_content_content = ""
        is_reasoning_streaming = False
        is_content_streaming = False
        complete_message = message
    else:
        # 其他类型的消息直接发送
        complete_message = message

    if not client_queues:
        return

    # 创建任务列表，并发发送消息
    tasks = []
    for client_id, queue in list(client_queues.items()):
        try:
            tasks.append(queue.put(complete_message))
        except Exception as e:
            logger.warning(f"Failed to queue message for client {client_id}: {e}")

    # 等待所有消息发送完成
    if tasks:
        await asyncio.gather(*tasks, return_exceptions=True)


# 历史消息配置
MAX_HISTORY_MESSAGES = 100  # 最大保存消息数量
CLEANUP_THRESHOLD = 120  # 当消息数量达到这个值时触发清理

# 初始消息
first_message = SSEMessage(
    type='Content',
    content='你好，我是大全小知，我可以帮你运营产品上的弱点和风险，你可以这样对我说："研判并处置弱点：W-250319-00014" 或者 "研判并处置风险：E-250323-00004"'
)
history_messages: List[SSEMessage] = [first_message]


def cleanup_history_messages():
    """清理历史消息，保留最近的消息"""
    global history_messages

    if len(history_messages) > CLEANUP_THRESHOLD:
        # 保留第一条消息（欢迎消息）和最近的消息
        recent_messages = history_messages[-MAX_HISTORY_MESSAGES:]

        # 如果第一条消息不在最近消息中，则保留它
        if history_messages[0] not in recent_messages:
            history_messages = [history_messages[0]] + recent_messages
        else:
            history_messages = recent_messages

        logger.info(f"清理历史消息，当前保留 {len(history_messages)} 条消息")


def add_to_history(message: SSEMessage):
    """安全地添加消息到历史记录，并在需要时清理"""
    global history_messages
    history_messages.append(message)

    # 每10条消息检查一次是否需要清理
    if len(history_messages) % 10 == 0:
        cleanup_history_messages()


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def sse_generator(client_id: str):
    """SSE消息生成器"""
    try:
        client_queue = client_queues[client_id]
        while True:
            message = await client_queue.get()
            yield f"data: {message.model_dump_json()}\n\n"
    except asyncio.CancelledError:
        logger.debug(f"客户端 {client_id} 连接被取消")
    except KeyError:
        logger.warning(f"客户端 {client_id} 队列不存在，可能已被清理")
    except Exception as e:
        logger.error(f"客户端 {client_id} SSE生成器出错: {e}")
    finally:
        # 清理客户端队列
        if client_id in client_queues:
            del client_queues[client_id]
        logger.debug(f"client_queues: {client_queues}")


@app.get("/sec-opt-agent/sse")
async def sse_endpoint(request: Request):
    # 为每个客户端创建唯一ID和队列
    client_id = str(uuid.uuid4())
    client_queues[client_id] = asyncio.Queue()

    # 发送历史消息到当前客户端
    for msg in history_messages:
        await client_queues[client_id].put(msg)

    # 如果系统正在处理任务，发送Start消息来禁用按钮
    if is_task_running:
        await client_queues[client_id].put(SSEMessage(type="Start"))

    # 如果当前有正在进行的流式消息，发送完整内容给新客户端
    if is_reasoning_streaming and current_reasoning_content:
        # 发送完整的推理内容作为单个消息
        await client_queues[client_id].put(SSEMessage(
            type="Reasoning",
            content=current_reasoning_content,
            is_partial=True
        ))

    if is_content_streaming and current_content_content:
        # 发送完整的回答内容作为单个消息
        await client_queues[client_id].put(SSEMessage(
            type="Content",
            content=current_content_content,
            is_partial=True
        ))

    return StreamingResponse(
        sse_generator(client_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


@app.post("/sec-opt-agent/message", response_model=StandardResponse)
async def process_message(request: Request):
    """处理用户消息"""
    global is_task_running

    body = await request.json()
    query = body.get('query')

    if not query:
        raise HTTPException(status_code=400, detail='Missing "query" field')

    if is_task_running:
        raise HTTPException(status_code=400, detail='Task is already running')

    try:
        is_task_running = True

        # 广播任务开始状态到所有客户端
        await broadcast_message(SSEMessage(type='Start'))

        # 添加用户消息到历史消息
        user_message = SSEMessage(type='Query', content=query)
        add_to_history(user_message)

        # 广播用户消息到所有客户端
        await broadcast_message(user_message)

        # agent执行任务
        await agent_run(query, broadcast_message, add_to_history)

        # 任务完成时更新状态
        is_task_running = False
        await broadcast_message(SSEMessage(type='Finish'))
        add_to_history(SSEMessage(type='Finish'))

        return StandardResponse(ok=True)

    except Exception as err:
        is_task_running = False
        await broadcast_message(SSEMessage(type='Error', content=f'err:{err}'))

        return StandardResponse(ok=False, error=str(err))


@app.post("/sec-opt-agent/conversation/reset", response_model=StandardResponse)
async def reset_conversation(request: Request):
    """重置对话历史"""
    global history_messages, current_reasoning_content, current_content_content, is_reasoning_streaming, is_content_streaming, is_task_running

    try:
        # 检查是否有任务正在运行
        if is_task_running:
            logger.warning("尝试在任务运行时重置对话，操作被拒绝")
            return StandardResponse(
                ok=False,
                error="无法重置：当前有任务正在运行，请等待任务完成后再试"
            )

        # 重置所有状态
        history_messages = [first_message]

        # 重置流式状态
        current_reasoning_content = ""
        current_content_content = ""
        is_reasoning_streaming = False
        is_content_streaming = False

        logger.info("对话历史已重置")

        # 广播重置状态到所有客户端
        await broadcast_message(SSEMessage(type='Finish'))
        await broadcast_message(first_message)

        return StandardResponse(ok=True)
    except Exception as err:
        logger.error(f"重置对话时出错: {err}")
        return StandardResponse(ok=False, error=str(err))


# 静态文件服务 - 添加具体的静态资源路由
@app.get("/sec-opt-agent/client.js")
async def client_js():
    """服务client.js文件"""
    js_path = cur_dir / "public" / "client.js"
    return FileResponse(js_path, media_type="application/javascript")

# 静态文件服务
@app.get("/sec-opt-agent")
async def index_html_no_slash():
    """服务前端文件"""
    index_path = cur_dir / "public" / "index.html"
    return FileResponse(index_path, media_type="text/html")

# 静态文件服务
@app.get("/sec-opt-agent/{path:path}")
async def index_html(path: str):
    """服务前端文件"""
    index_path = cur_dir / "public" / "index.html"
    return FileResponse(index_path, media_type="text/html")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=3000)

"""
数据模型定义
"""
from typing import Optional, Literal

from pydantic import BaseModel


class SSEMessage(BaseModel):
    """SSE消息模型"""
    type: Literal['Start', 'Finish', 'Reasoning', 'ReasoningDone', 'Content', 'ContentDone', 'ToolCall', 'ToolResult', 'Query', 'Error']
    tool_name: Optional[str] = None
    content: Optional[str] = None
    is_partial: Optional[bool] = False


class StandardResponse(BaseModel):
    """标准响应模型"""
    ok: bool
    error: Optional[str] = None

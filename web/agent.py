import asyncio
from pathlib import Path

from agents import (
    Agent,
    Runner,
    set_default_openai_api,
    set_default_openai_client,
    set_tracing_disabled,
)
from bson import ObjectId
from openai import AsyncOpenAI
from openai.types.responses import (
    ResponseReasoningSummaryTextDeltaEvent,
    ResponseReasoningSummaryPartDoneEvent,
    ResponseTextDeltaEvent,
    ResponseContentPartDoneEvent,
    ResponseOutputItemDoneEvent,
    ResponseFunctionToolCall
)

from tp_utils.log_util import TLogger
from tp_utils.nacos_util import config
from .mcp_manager import McpManager
from .models import SSEMessage
from .prompt import SYSTEM_PROMPT

logger = TLogger.get_logger(__name__)
from tp_utils.mongo_util import MONGO_UTIL
from typing import List

from agents.mcp import MCPServerStdio

AGENT = None


class SecAgent:
    def __init__(self, mcp_servers: List[MCPServerStdio]):
        # 获取LLM config
        llm_config_id_path = Path(config['web']['llm_config_id_path'])
        llm_cfg_id = llm_config_id_path.read_text()
        llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one(
            {"_id": ObjectId(llm_cfg_id), "flag": True, "delFlag": False})
        if not llm_cfg:
            raise ValueError("No LLM config found")
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        # 设置agents自定义client
        client = AsyncOpenAI(
            base_url=BASE_URL,
            api_key=API_KEY,
        )
        set_default_openai_client(client=client, use_for_tracing=False)
        set_default_openai_api("chat_completions")
        set_tracing_disabled(disabled=True)

        self.agent = Agent(
            name="QzkjSecAgent",
            instructions=SYSTEM_PROMPT,
            model=self.MODEL_NAME,
            mcp_servers=mcp_servers,
        )

    @classmethod
    async def from_mcp_config(cls, config_path: str):
        mcp_servers = await McpManager.get_servers(config_path)
        return cls(mcp_servers)

    async def run(self, prompt: str, broadcast_func: callable, add_to_history_func: callable):
        result = Runner.run_streamed(self.agent, input=prompt)
        try:
            async for event in result.stream_events():
                if event.type == "raw_response_event":
                    if isinstance(event.data, ResponseReasoningSummaryTextDeltaEvent):
                        await broadcast_func(SSEMessage(type="Reasoning", content=event.data.delta, is_partial=True))
                    elif isinstance(event.data, ResponseReasoningSummaryPartDoneEvent):
                        await broadcast_func(SSEMessage(type="ReasoningDone"))
                        add_to_history_func(
                            SSEMessage(type="Reasoning", content=event.data.part.text, is_partial=False))
                    elif isinstance(event.data, ResponseOutputItemDoneEvent) and isinstance(event.data.item,
                                                                                            ResponseFunctionToolCall):
                        await broadcast_func(SSEMessage(type="ToolCall", tool_name=event.data.item.name,
                                                        content=event.data.item.arguments))
                        add_to_history_func(SSEMessage(type="ToolCall", tool_name=event.data.item.name,
                                                       content=event.data.item.arguments))
                    elif isinstance(event.data, ResponseTextDeltaEvent):
                        await broadcast_func(SSEMessage(type="Content", content=event.data.delta, is_partial=True))
                    elif isinstance(event.data, ResponseContentPartDoneEvent):
                        await broadcast_func(SSEMessage(type="ContentDone"))
                        add_to_history_func(SSEMessage(type="Content", content=event.data.part.text, is_partial=False))
                    # else:
                    #     print(str(type(event.data)))
                elif event.type == 'run_item_stream_event' and event.name == "tool_output":
                    await broadcast_func(SSEMessage(type="ToolResult", content=event.item.output))
                    add_to_history_func(SSEMessage(type="ToolResult", content=event.item.output))
                # else:
                #     print(f"event.type:{event.type}, event.name:{event.name if event.type == 'run_item_stream_event' else None}")
        except Exception as e:
            logger.error(f"运行agent出错: {e}")
            raise
        # finally:
        #     # 确保流被正确关闭
        #     result.cancel()

    async def cleanup(self):
        """清理资源"""
        try:
            await McpManager.clear_servers()
        except Exception as e:
            logger.error(f"清理MCP服务器时出错: {e}")
            raise


async def agent_run(query: str, broadcast_func: callable, add_to_history_func: callable):
    global AGENT
    try:
        if not AGENT:
            AGENT = await SecAgent.from_mcp_config(config['mcp']['config_path'])
        await AGENT.run(query, broadcast_func, add_to_history_func)
    except Exception as e:
        logger.error(f"运行代理时出错: {e}")
        raise
    # finally:
    #     if AGENT:
    #         await AGENT.cleanup()


if __name__ == "__main__":
    # 启动web先存储llm配置，先这样实现
    llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one({"flag": True, "delFlag": False})
    if not llm_cfg:
        raise ValueError("No LLM config found")
    llm_cfg_id = str(llm_cfg['_id'])
    llm_config_id_path = Path(config['web']['llm_config_id_path'])
    llm_config_id_path.write_text(llm_cfg_id)

    async def my_print(content):
        pass
        # print(content)


    def my_add_to_history(message):
        print(f"添加到历史: {message}")


    asyncio.run(agent_run("天空为什么是蓝色的？", my_print, my_add_to_history))

import json
from typing import List

from agents.mcp import MCPServerStdio

from tp_utils.log_util import TLogger

logger = TLogger.get_logger(__name__)


class McpManager:
    mcp_servers: List[MCPServerStdio] = []

    @staticmethod
    def load_mcp_settings(file_path: str) -> dict:
        with open(file_path, 'r') as f:
            return json.load(f)

    @classmethod
    async def get_servers(cls, file_path: str) -> List[MCPServerStdio]:
        settings = cls.load_mcp_settings(file_path)
        servers = settings.get("mcpServers", {})
        for name, params in servers.items():
            if not params.get("enabled"):
                continue
            server = MCPServerStdio(
                name=name,
                params=params,
            )
            await server.connect()
            cls.mcp_servers.append(server)
        return cls.mcp_servers

    @classmethod
    async def clear_servers(cls):
        """清理所有MCP服务器连接"""
        for server in cls.mcp_servers:
            try:
                await server.cleanup()
            except Exception as e:
                # 记录错误但不抛出异常，确保所有服务器都被尝试清理
                logger.error(f"清理服务器 {server.name} 时出错: {e}")
        cls.mcp_servers = []

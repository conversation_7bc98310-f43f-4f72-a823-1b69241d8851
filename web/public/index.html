<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <title>AI助手</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="AI Chat Interface" />
    <style>
        :root {
            --primary-color: #2563eb;
            --bg-color: #f8fafc;
            --chat-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --user-message-bg: #eff6ff;
            --assistant-message-bg: #ffffff;
            --internal-message-bg: #f1f5f9;
            --tool-block-bg: #fef3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
        }

        #chatContainer {
            max-width: 1000px;
            margin: 1rem auto;
            padding: 0 1rem;
            height: calc(100vh - 2rem);
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 1rem;
            padding-top: 1rem;
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h2 {
            font-size: 1.75rem;
            font-weight: 700;
        }

        #clearBtn {
            background: white;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
        }

        #clearBtn:hover {
            background: #f1f5f9;
        }

        #chatLog {
            flex: 1;
            height: 0;
            background: var(--chat-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.25rem;
            overflow-y: auto;
            margin-bottom: 1rem;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .message-row {
            margin: 0.5rem 0;
            display: flex;
            gap: 0.75rem;
        }

        .message-row.user-message {
            justify-content: flex-end;
        }

        .bubble {
            max-width: 85%;
            padding: 0.6rem 0.8rem;
            font-size: 0.875rem;
            border-radius: 1.2rem;
        }

        .bubble.user {
            background: var(--user-message-bg);
            border: 1px solid #bfdbfe;
            border-bottom-right-radius: 0.3rem;
            max-width: 60%;
        }

        .bubble.assistant {
            background: var(--assistant-message-bg);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 0.3rem;
        }

        .bubble.internal {
            background: var(--internal-message-bg);
            font-size: 0.875rem;
            color: var(--text-secondary);
            max-width: 100%;
            text-align: center;
        }

        .tool-block {
            background: var(--tool-block-bg);
            border-radius: 0.8rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-size: 0.875rem;
            max-width: 75%;
            min-width: 280px;
        }

        .tool-row {
            margin: 0.5rem 0;
            display: flex;
            width: 100%;
            justify-content: flex-start;
        }

        .tool-block details {
            background: transparent;
            border: none;
            padding: 0;
            margin: 0;
            width: 100%;
        }

        .tool-block summary {
            padding: 0.2rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
            list-style: none;
        }

        .tool-block summary::-webkit-details-marker {
            display: none;
        }

        .tool-row .tool-block summary::before {
            content: '▶';
            font-size: 0.75rem;
            margin-right: 0.5rem;
            transition: transform 0.2s ease;
            color: var(--text-secondary);
            display: inline-block;
        }

        .tool-row .tool-block details[open] summary::before {
            transform: rotate(90deg) !important;
        }

        .tool-row .tool-block summary:hover::before {
            color: #374151;
        }

        .tool-block pre {
            margin-top: 0.5rem;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.5);
            padding: 0.5rem;
        }

        /* 思考内容样式 */
        .reasoning-row {
            margin: 0.5rem 0;
            display: flex;
            width: 100%;
            justify-content: flex-start;
        }

        .reasoning-block {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 0.75rem;
            padding: 0.75rem;
            max-width: 80%;
            min-width: 300px;
        }

        .reasoning-details {
            background: transparent;
            border: none;
            padding: 0;
            margin: 0;
            width: 100%;
        }

        .reasoning-summary {
            padding: 0.2rem 0;
            font-size: 0.875rem;
            color: #0369a1;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
        }

        .reasoning-summary::-webkit-details-marker {
            display: none;
        }

        .reasoning-row .reasoning-summary::before {
            content: '▶';
            font-size: 0.75rem;
            transition: transform 0.2s ease;
            color: #0369a1;
            display: inline-block;
        }

        .reasoning-row .reasoning-details[open] .reasoning-summary::before {
            transform: rotate(90deg);
        }

        .reasoning-summary:hover {
            color: #0284c7;
        }

        .reasoning-summary:hover::before {
            color: #0284c7;
        }

        .reasoning-icon {
            font-size: 1rem;
        }

        .reasoning-text {
            font-weight: 500;
        }

        .reasoning-content {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #1e40af;
            background: rgba(255, 255, 255, 0.6);
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 3px solid #3b82f6;
        }

        .reasoning-content pre {
            background: rgba(255, 255, 255, 0.8);
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
        }

        /* 思考中的动态点点动画 */
        .thinking-dots {
            display: inline-block;
        }

        .thinking-dots::after {
            content: '';
            animation: thinking 1.5s infinite;
        }

        @keyframes thinking {
            0% { content: ''; }
            25% { content: '.'; }
            50% { content: '..'; }
            75% { content: '...'; }
            100% { content: ''; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .reasoning-block {
                max-width: 95%;
                min-width: 250px;
            }

            .tool-block {
                max-width: 95%;
                min-width: 250px;
            }
        }

        @media (max-width: 480px) {
            .reasoning-block {
                max-width: 100%;
                min-width: auto;
            }

            .tool-block {
                max-width: 100%;
                min-width: auto;
            }
        }

        #inputRow {
            gap: 0.5rem;
            margin-top: auto;
            margin-bottom: 1.25rem;
            display: grid;
            grid-template-columns: 1fr auto;
            align-items: center;
            position: sticky;
            bottom: 0;
            background: var(--bg-color);
            padding-top: 0.5rem;
        }

        #queryInput {
            padding: 0.6rem 1rem;
            font-size: 1rem;
            height: auto;
            min-height: 48px;
            max-height: 150px;
            border-radius: 0.75rem;
            resize: none;
            overflow-y: auto;
            line-height: 1.5;
        }

        #queryInput:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        #sendBtn {
            height: 48px;
            padding: 0 1.5rem;
            font-size: 1rem;
            border-radius: 0.75rem;
            background: var(--primary-color);
            color: white;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }

        #sendBtn:hover {
            background-color: #1d4ed8;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background-color: var(--text-secondary);
            border-radius: 50%;
            opacity: 0.4;
            animation: typingAnimation 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-3px);
                opacity: 0.8;
            }
        }

        .message-row.loading {
            margin-top: 0.5rem;
        }

        .bubble.loading {
            background: var(--assistant-message-bg);
            padding: 0.4rem 0.8rem;
            border: 1px solid var(--border-color);
        }

        /* Code blocks styling */
        pre {
            background: #f8fafc;
            border-radius: 0.375rem;
            padding: 0.75rem;
            overflow-x: auto;
            font-size: 0.8rem;
        }

        code {
            font-family: ui-monospace, monospace;
            font-size: 0.8rem;
        }

        /* Mobile responsiveness */
        @media (max-width: 640px) {
            #chatContainer {
                height: calc(100vh - 1rem);
                margin: 0.5rem auto;
            }

            .bubble {
                max-width: 85%;
            }

            .bubble.user {
                max-width: 75%;
                font-size: 0.85rem;
            }

            #inputRow {
                gap: 0.5rem;
            }

            #sendBtn {
                padding: 0 1rem;
            }

            .tool-block {
                max-width: 100%;
            }
        }

    </style>
</head>
<body>
<div id="chatContainer">
    <div class="header">
        <div class="header-row">
            <h2>AI 助手</h2>
            <button id="clearBtn">Clear Chat</button>
        </div>
    </div>

    <div id="chatLog"></div>

    <div id="inputRow">
        <textarea
            id="queryInput"
            rows="3"
            placeholder='研判并处置弱点:W-250519-00014/研判并处置风险:E-250523-00004'
        ></textarea>
        <button id="sendBtn">Send</button>
    </div>
</div>
<script src="./sec-opt-agent/client.js"></script>
<script>
    document.getElementById('queryInput').addEventListener('input', function() {
        this.style.height = 'auto';
        const newHeight = Math.min(this.scrollHeight, 150);
        this.style.height = newHeight + 'px';
    });
</script>
</body>
</html>

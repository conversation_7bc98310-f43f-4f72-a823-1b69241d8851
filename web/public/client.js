// ================== DOM ELEMENTS & GLOBAL STATE ==================
const chatLog = document.getElementById('chatLog');
const clearBtn = document.getElementById('clearBtn');
const queryInput = document.getElementById('queryInput');
const sendBtn = document.getElementById('sendBtn');

// 如果是用户消息，添加输入中状态
const loadingRow = document.createElement('div');
loadingRow.className = 'message-row';
loadingRow.innerHTML = `
    <div class="bubble loading">
        <div class="typing-indicator">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    </div>
`;

let reasoningPartialBubble = document.createElement('div');
let isReasoningPartialBubbleInit = false;
let contentPartialBubble = document.createElement('div');
let isContentPartialBubbleInit = false;
// ================== SSE CONNECTION SETUP ==================
const eventSource = new EventSource('/sec-opt-agent/sse');

// Handle incoming SSE messages
eventSource.onmessage = (event) => {
    let data;
    try {
        data = JSON.parse(event.data);
    } catch {
        console.warn('Could not parse event as JSON:', event.data);
        return;
    }

    switch (data.type) {
        case 'Reasoning':
            if (data.is_partial){
                appendReasoningPartialMessage(data.content);
            }else {
                appendReasoningMessage(data.content);
            }
            break;
        case 'ReasoningDone':
            // 更新思考完成状态
            if (reasoningPartialBubble && reasoningPartialBubble.querySelector) {
                const summaryText = reasoningPartialBubble.querySelector('.reasoning-text');
                if (summaryText) {
                    summaryText.textContent = '思考完成';
                }
                const icon = reasoningPartialBubble.querySelector('.reasoning-icon');
                if (icon) {
                    icon.textContent = '💭';
                }
            }
            // 重置状态，准备下一次思考
            reasoningPartialBubble = null;
            isReasoningPartialBubbleInit = false;
            break;
        case 'Content':
            if (data.is_partial){
                appendContentPartialMessage(data.content);
            }else {
                appendContentMessage(data.content);
            }
            break;
        case 'ContentDone':
            contentPartialBubble = document.createElement('div');
            isContentPartialBubbleInit = false;
            break;
        case 'ToolCall':
            appendToolCallMessage(data.tool_name, data.content);
            break;
        case 'ToolResult':
            appendToolResultMessage(data.content);
            break;
        case 'Query':
            appendUserMessage(data.content);
            break;
        case 'Start':
            setSendButtonState(false);
            setClearButtonState(false);
            break;
        case 'Finish':
            setSendButtonState(true);
            setClearButtonState(true);
            loadingRow.remove();
            break;
        case 'Error':
            setSendButtonState(true);
            setClearButtonState(true);
            appendErrMessage('internal', data.content);
            break;
    }
};

// Handle SSE errors
eventSource.onerror = (err) => {
    console.error('SSE error:', err);
    // appendErrMessage('internal', `SSE error: ${JSON.stringify(err.message) || err}`);
};


function appendErrMessage(role, content) {
    const row = document.createElement('div');
    row.className = 'message-row';
    row.classList.add('internal-message');

    const bubble = document.createElement('div');
    bubble.className = `bubble ${role}`;
    bubble.innerHTML = formatAnyContent(content);

    row.appendChild(bubble);
    chatLog.appendChild(row);
    chatLog.scrollTop = chatLog.scrollHeight;
}

function appendReasoningPartialMessage(content) {
    if (content === '') return;
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }

    if (isReasoningPartialBubbleInit === false) {
        const row = document.createElement('div');
        row.className = 'message-row reasoning-row';

        const container = document.createElement('div');
        container.className = 'reasoning-block';

        reasoningPartialBubble = document.createElement('details');
        reasoningPartialBubble.className = 'reasoning-details';
        reasoningPartialBubble.innerHTML = `
            <summary class="reasoning-summary">
                <span class="reasoning-icon">🤔</span>
                <span class="reasoning-text">思考中<span class="thinking-dots"></span></span>
            </summary>
            <div class="reasoning-content">${formatAnyContent(content)}</div>
        `;

        container.appendChild(reasoningPartialBubble);
        row.appendChild(container);
        chatLog.appendChild(row);

        isReasoningPartialBubbleInit = true;
    } else {
        // 直接替换内容而不是拼接，因为后端发送的是完整内容
        const contentDiv = reasoningPartialBubble.querySelector('.reasoning-content');
        if (contentDiv) {
            contentDiv.innerHTML = formatAnyContent(content);
        }
    }

    // 滚动到底部
    chatLog.scrollTop = chatLog.scrollHeight;

    //添加输入状态
    chatLog.appendChild(loadingRow);
}

function appendReasoningMessage(content) {
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }
    // 显示完整的思考内容（用于历史消息）
    const row = document.createElement('div');
    row.className = 'message-row reasoning-row';

    const container = document.createElement('div');
    container.className = 'reasoning-block';

    const reasoningDetails = document.createElement('details');
    reasoningDetails.className = 'reasoning-details';
    reasoningDetails.innerHTML = `
        <summary class="reasoning-summary">
            <span class="reasoning-icon">💭</span>
            <span class="reasoning-text">思考完成</span>
        </summary>
        <div class="reasoning-content">${formatAnyContent(content)}</div>
    `;

    container.appendChild(reasoningDetails);
    row.appendChild(container);
    chatLog.appendChild(row);

    // 滚动到底部
    chatLog.scrollTop = chatLog.scrollHeight;

    //添加输入状态
    chatLog.appendChild(loadingRow);
}

function appendContentPartialMessage(content) {
    if (content === '') return;
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }
    if (isContentPartialBubbleInit === false) {
        const row = document.createElement('div');
        row.className = 'message-row';
        row.classList.add('assistant-message');

        contentPartialBubble.className = `bubble assistant`;
        contentPartialBubble.innerHTML = formatAnyContent(content);

        row.appendChild(contentPartialBubble);
        chatLog.appendChild(row);

        isContentPartialBubbleInit = true;
    } else {
        // 直接替换内容而不是拼接，因为后端发送的是完整内容
        contentPartialBubble.innerHTML = formatAnyContent(content);
    }

    // 滚动到底部
    chatLog.scrollTop = chatLog.scrollHeight;

    //添加输入状态
    chatLog.appendChild(loadingRow);
}


function appendUserMessage(content) {
    // 删除输入中的状态
    if (loadingRow.parentNode === chatLog) {
        loadingRow.remove();
    }

    const row = document.createElement('div');
    row.className = 'message-row';
    row.classList.add('user-message');

    const bubble = document.createElement('div');
    bubble.className = `bubble user`;
    bubble.innerHTML = formatAnyContent(content);

    row.appendChild(bubble);
    chatLog.appendChild(row);

    //添加输入状态
    chatLog.appendChild(loadingRow);
}

function appendContentMessage(content) {
    if (content.trim() === '') return;
    const row = document.createElement('div');
    row.className = 'message-row';
    row.classList.add('assistant-message');

    const bubble = document.createElement('div');
    bubble.className = `bubble assistant`;
    bubble.innerHTML = formatAnyContent(content);

    row.appendChild(bubble);
    chatLog.appendChild(row);
}


function appendToolCallMessage(tool_name, content) {
    const row = document.createElement('div');
    row.className = 'message-row tool-row';

    const container = document.createElement('div');
    container.className = 'tool-block';

    // 创建details元素
    const details = document.createElement('details');
    const summary = document.createElement('summary');
    summary.textContent = `Tool use: ${tool_name}`;

    const contentDiv = document.createElement('div');
    contentDiv.innerHTML = formatAnyContent(content);

    details.appendChild(summary);
    details.appendChild(contentDiv);
    container.appendChild(details);

    row.appendChild(container);
    chatLog.appendChild(row);
    chatLog.scrollTop = chatLog.scrollHeight;
}

function appendToolResultMessage(content) {
    const row = document.createElement('div');
    row.className = 'message-row tool-row';

    const container = document.createElement('div');
    container.className = 'tool-block';

    // 创建details元素
    const details = document.createElement('details');
    const summary = document.createElement('summary');
    summary.textContent = 'Tool Result:';

    const contentDiv = document.createElement('div');
    contentDiv.innerHTML = formatAnyContent(content);

    details.appendChild(summary);
    details.appendChild(contentDiv);
    container.appendChild(details);

    row.appendChild(container);
    chatLog.appendChild(row);
    chatLog.scrollTop = chatLog.scrollHeight;
}

// ================== UTILITY FOR FORMATTING CONTENT (JSON, MD, ETC.) ==================
function formatAnyContent(content) {
    if (typeof content === 'string') {
        // Try JSON parse
        try {
            const obj = JSON.parse(content);
            return `<pre>${escapeHTML(JSON.stringify(obj, null, 2))}</pre>`;
        } catch {
            // fallback to markdown
            return formatMarkdown(content);
        }
    }

    // fallback
    return String(content);
}

/** A naive Markdown transform */
function formatMarkdown(text) {
    let safe = escapeHTML(text);
    // code fences
    safe = safe.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
    // inline code
    safe = safe.replace(/`([^`]+)`/g, '<code>$1</code>');
    // bold, italics, links, newlines
    safe = safe.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    safe = safe.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    safe = safe.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
    safe = safe.replace(/\n/g, '<br>');
    return safe;
}

/** HTML escaper for <pre> blocks, etc. */
function escapeHTML(str) {
    if (typeof str !== 'string') return String(str);
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
}


function setSendButtonState(enabled) {
    sendBtn.disabled = !enabled;
    sendBtn.style.opacity = enabled ? '1' : '0.5';
    sendBtn.style.cursor = enabled ? 'pointer' : 'not-allowed';
}

function setClearButtonState(enabled) {
    clearBtn.disabled = !enabled;
    clearBtn.style.opacity = enabled ? '1' : '0.5';
    clearBtn.style.cursor = enabled ? 'pointer' : 'not-allowed';
}

async function sendQuery(query) {
    if (!query.trim()) return;

    try {
        const resp = await fetch('/sec-opt-agent/message', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({query}),
        });
        const data = await resp.json();
        if (data.error) {
            appendErrMessage('internal', `Server error: ${data.error}`);
        }
    } catch (err) {
        appendErrMessage('internal', `Network error: ${err.message}`);
    }
}


clearBtn.addEventListener('click', async () => {
    try {
        const resp = await fetch('/sec-opt-agent/conversation/reset', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({})
        });

        const data = await resp.json();

        if (data.ok) {
            // 重置成功，清空聊天记录（SSE会发送新的欢迎消息）
            chatLog.innerHTML = '';
            console.log('对话历史重置成功');
        } else {
            // 重置失败，显示错误信息
            appendErrMessage('internal', `重置失败: ${data.error || '未知错误'}`);
            console.error('重置对话失败:', data.error);
        }
    } catch (err) {
        console.error('Error resetting conversation:', err);
        appendErrMessage('internal', `重置对话时发生网络错误: ${err.message}`);
    }
});


sendBtn.addEventListener('click', () => {
    const query = queryInput.value.trim();
    if (query) {
        sendQuery(query);
        queryInput.value = '';
    }
});

queryInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        sendBtn.click();
    }
});

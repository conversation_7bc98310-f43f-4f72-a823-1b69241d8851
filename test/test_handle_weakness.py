import json
import time
from pathlib import Path

from tasks.handle_weakness import HandleWeakness
from nacos_util import NACOS_UTIL

WEAKNESS_MAP = NACOS_UTIL.get_weakness_desc()
# WEAKNESS_MAP = {'CORS': 'CORS(跨源资源共享)，当允许凭据（如 Cookie、HTTP 认证信息等）时，不能使用通配符 \"*\" 作为允许的源。必须明确指定允许的源（如请求中的 Origin），否则可能导致恶意网站利用用户凭据进行跨站请求，从而产生安全风险。', 'HTTP协议传输敏感数据': 'API使用了HTTP协议进行通信，传输的敏感数据易被中间人拦截和窃取，并缺乏数据完整性保护。', 'JWT存在敏感数据': 'JWT中存储了用户个人敏感信息，若JWT泄露则攻击者可轻易获取JWT中的敏感数据。', 'JWT弱秘钥': 'JWT凭证秘钥太简单或没有使用秘钥，攻击者可以通过构造JWT凭证拿到账号的权限。', 'JWT无签名算法': 'JWT使用了无签名算法，当alg字段为空时，后端将不执行签名验证，攻击者可构造出JWT凭证进行身份信息伪造。', 'JWT过期时间设置过长': 'JWT过期时间设置越长意味着有效JWT的时间就越长，相应的攻击成功率也会越高，如攻击者利用本应该失效的JWT进行会话劫持攻击。', 'SSRF': 'SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。', 'WebShell': 'WebShell是黑客经常使用的一种恶意脚本，其目的是获得服务器的执行操作权限，比如执行系统命令、窃取用户数据、删除web页面、修改主页等。', 'cookie没有设置为httpOnly': '未设置 HttpOnly 的 Cookie 可被客户端脚本访问，易在跨站脚本攻击（XSS）中被窃取，进而导致会话劫持等安全问题。', '代码仓库暴露': '源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。', '任意文件读取': '接口中发现直接传递文件路径的参数或可穿越目录的恶意参数，恶意攻击者可能通过修改参数访问服务器中的任意文件，导致服务器中的重要数据文件泄漏。', '任意短信发送': '在发送短信的接口中，在请求参数中发现手机号码和短信内容，恶意攻击者可能利用接口给指定手机发送恶意短信。', '允许上传恶意文件': '可通过接口上传特殊后缀名的文件，如果上传jsp、php等可能导致代码执行漏洞，上传html、js等文件进行xss攻击或钓鱼。', '单次返回所有数据': '接口可以直接访问，不用构造参数，可一次性获取大量敏感数据。', '参数可遍历': '在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本进行批量的数据拉取。', '可遍历下载文件': '在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本批量下载文件。', '命令执行': '在接口的请求参数中发现可以执行的系统命令，攻击者可以通过修改参数，绕过系统的限制执行系统命令，可能导致系统中重要数据泄漏、攻击者获取服务器控制权限或造成系统服务不可用。', '在cookie中保存密码': '接口设计中，将用户密码保存在cookie中，可能被中间人攻击拦截获取密码信息，或在浏览器被他人借用的情况下泄漏密码信息。', '备份文件泄露': '应用下backup.zip、backup.rar等备份文件可以被下载，其中可能包含代码或敏感数据备份，导致信息或敏感数据泄露。', '存储桶ListObject权限公开': '存储桶设置了ListObject权限公开，如果存储桶中存储了敏感数据，例如用户个人信息、机密文件等，那么公开ListObject权限可能会导致敏感信息未经授权访问，从而造成严重的信息泄露。', '存储桶域名劫持': '云存储桶已删除，但对应的DNS CNAME记录未被删除，自定义域名与云存储桶域名间仍存在映射关系，此时攻击者可注册同名云存储桶进行域名劫持。', '密码透出': '在接口返回内容中发现密码信息，攻击者可在传输过程中进行监听或拦截，从而获取密码信息。', '接口信息泄露': '接口文档暴露，增加了接口资产暴露面，攻击者可以根据接口文档对接口进行更深入的测试和利用。', '接口存在验证码返回': '验证码值在响应中返回了，攻击者能够直接通过接口响应获取验证码值，从而伪造身份或绕过验证。', '敏感信息在URL中': 'URL中发现关键身份证号码或手机号等敏感信息，url会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被窥视。', '敏感文件泄露': '攻击者可以直接访问接口下载带有敏感信息的文件，从而获取敏感数据。', '数据库查询': '在接口的请求参数中发现可以执行的数据库查询语句，攻击者可以通过修改参数，绕过系统的限制查询系统中的重要数据，甚至执行恶意语句，造成系统数据被破坏。', '数据库连接信息泄漏': '接口返回数据中泄漏了不必要的数据库连接信息，可能导致被攻击者获取后，利用连接信息访问数据库。', '明文密码传输': '在接口请求中发现明文密码传输，攻击者可在传输过程中进行监听或拦截，从而获取用户真实密码。', '明文密码透出': '在接口的返回内容中发现明文密码，并且密码没有进行hash就直接以明文保存在了数据库中，服务器不应该以任何形式保存明文密码信息。', '更新密码接口设计不合理': '更新密码接口未校验旧密码，可能存在一定的安全风险，如账号盗用导致密码被篡改。', '更新用户接口设计不规范': '更新用户接口中除去一般的用户信息还包含密码字段，存在潜在的安全风险，如账号借用导致密码修改、结合CSRF漏洞导致密码篡改等。', '服务端请求伪造': 'SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。', '未禁用目录浏览': 'apache等服务配置时未禁用目录浏览，可能导致项目文件泄漏，如日志文件、项目配置、备份文件等。', '未鉴权': '可查询敏感数据或进行敏感操作的接口可以在未鉴权的情况下访问，攻击者可以不登录就访问接口从而获取大量敏感数据或执行敏感操作。', '权限更新不合理': '接口请求中存在角色权限字段，用户可以修改该字段来更新自身角色权限，从而导致越权。', '源代码泄露': '源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。', '登录弱密码': '在登录接口的请求中存在弱密码，弱密码可能通过账号暴力破解的方式被破解。', '登录接口缺乏速率限制': '登录接口缺乏速率限制，攻击者可以使用暴力破解等方式进行恶意登录，从而访问系统中的敏感信息或进行其他不当操作。', '登录认证不合理': '发现登录接口或其他需要授权的接口使用URL参数传递密码，可能导致URL中的密码信息在请求日志中泄漏，或在浏览器页面被他人通过窥视泄漏。', '登录错误提示不合理': '攻击者根据返回的提示信息可能枚举出系统中存在的登录用户名，再对其密码进行暴力破解或者根据收集到的用户名进行其他更高级的攻击。', '登录页面存在账号密码': '网页源代码中存在账号密码等敏感信息，攻击者可以通过审查网页源代码获取到用户账号和密码，导致用户认证信息泄露。', '脆弱应用在公网暴露': 'gitlab,nexus等开发环境的应用在公网可访问，可能导致代码泄漏或重要的信息泄漏。', '脱敏不合规': '在接口事件的返回中发现，敏感数据脱敏不规范，脱敏位数不足。', '脱敏策略不一致': '在接口事件的返回中发现，对同一个敏感数据存在已脱敏和未脱敏数据同时返回给前端的行为,可以通过查看接口详细的请求和返回找出未脱敏的原始敏感数据，未真正达到数据脱敏的目的。', '请求权限参数可控': '请求参数中存在权限判断相关字段，攻击者可能通过修改权限字段值来提升自己的权限。', '请求路径异常': '接口路径中包含异常请求字符，可能存在鉴权绕过访问行为。', '调试信息泄露': '在接口的返回数据中发现调试信息。调试信息可能泄露应用内部的敏感状态或配置详情，从而导致应用或平台数据存在泄漏风险。', '账号名可枚举': '攻击者可以通过接口查询来确定系统中存在的有效用户名，从而进行密码猜测或其他攻击，例如暴力破解、社会工程学攻击等。', '过期的JWT未失效': '本应过期的JWT仍通过权限验证。', '返回数据量可修改': '在接口的请求中发现与返回数量相关的查询参数，可以通过修改参数，单次获取大量敏感数据。', '配置文件泄露': '配置文件可以被下载，可能造成数据库连接信息、API密钥等敏感信息泄露。', '鉴权信息在URL中': 'URL中发现Token或SessionID等鉴权信息，URL中的信息会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被他人获取。', '鉴权凭证脆弱': '接口使用了手机号、身份证号等个人敏感信息作为鉴权凭证，导致鉴权凭证易被猜测和伪造，攻击者可修改鉴权凭证为其他用户对应的手机号、身份证号等字段值绕过权限校验，从而获取敏感数据。', '非必要的数据暴露': '可疑的查询请求，造成非必要透出的敏感数据暴露。', '验证码认证接口缺乏速率限制': '验证码认证接口缺乏速率限制，攻击者可以使用自动化脚本进行恶意尝试，从而绕过验证码认证进行攻击。'}


def get_weakness_unittest():
    limit = 1000 # body 截断长度
    p = Path('/Users/<USER>/Desktop/qz/sec-opt-agent/测试样例/弱点样例/江西电信弱点样例')
    for txt_file in p.rglob('*.http'):
        content = txt_file.read_text(encoding='utf-8')

        request_part = content.split('### Request', 1)[1].split('### Response')[0].strip()
        response_part = content.split('### Response', 1)[1].strip()

        # 提取请求头和请求体
        if '\n\n' in request_part:
            request_headers, request_body = request_part.split('\n\n', 1)
            request_body = request_body.strip()
        else:
            request_headers = request_part
            request_body = ""

        # 提取响应头和响应体
        if '\n\n' in response_part:
            response_headers, response_body = response_part.split('\n\n', 1)
            response_body = response_body.strip()
        else:
            response_headers = response_part
            response_body = ""

        # 截断
        truncated_request_body = request_body[:limit]
        truncated_response_body = response_body[:limit]

        req = (f"{request_headers}\n\n"
        f"{truncated_request_body}\n\n")
        rsp = (f"{response_headers}\n\n"
        f"{truncated_response_body}\n")

        yield txt_file.name,req,rsp

def test_weakness_unittest():
    h = HandleWeakness()
    h._init()
    token_usage = {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0
    }
    count = 0

    for filename,req,rsp in get_weakness_unittest():
        count += 1
        weakness_name = filename.split("_")[0]
        if weakness_name not in WEAKNESS_MAP:
            print(f"找不到弱点定义：{weakness_name}")
            continue
        prompt = (f"<弱点名称>{weakness_name}</弱点名称>"
                  f"<弱点定义>{WEAKNESS_MAP[weakness_name]}</弱点定义>"
                  f"<http日志>{req}\n{rsp}</http日志>")
        result = h.analyze_weakness(prompt, token_usage)

        if count % 10 == 0:
            print(f"第{count}个")

        if "误报" in filename:
            answer = "已忽略"
        else:
            answer = "待修复"
        if result['result'] != answer:
            print(f"ERROR: AI判断错误：{filename}, 结论为：{result}")
        else:
            print(f"AI判断正确：{filename}，结论为：{result}")


if __name__ == '__main__':
    # test_handle_weakness(800)
    test_weakness_unittest()



from  tasks.extract_scan_regex_task import ExtractScanRegex

p = ExtractScanRegex()
p._init()

samples = {"https://www.zhuangku.com/login":{"扫描样例1": {
    "req": {
        "body": "{\"username\": \"admin\", \"password\": \"123456\"}",
        "remoteAddr": "",
        "url": "https://www.zhuangku.com/login",
        "header": {
            "content-length": "95",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "dnt": "1",
            "x-forwarded-for": "**************",
            "pragma": "no-cache",
            "accept": "application/json, text/javascript, */*; q=0.01",
            "x-xsrf-token": "oaqpLWXONNvapm9tEbtm-_vneTDYIqJuaAtG14uSaI7QWc1PmsA-BcAg44n4LpFwb_xa3N-fdoz8tuVkOiKu50eMcjnw3YtUwMDbT_96KRo1",
            "host": "www.zhuangku.com",
            "x-requested-with": "XMLHttpRequest",
            "content-type": "application/json",
            "connection": "close",
            "cache-control": "no-cache",
            "accept-encoding": "gzip, deflate",
            "user-agent": "iphone"
        },
        "method": "POST",
        "postArgs": {
            "password": "123456",
            "username": "admin"
        },
        "httpVersion": "1.1"
    },
    "rsp": {
        "status": "200",
        "header": {
            "date": "Wed, 18 Aug 2021 02:55:45 GMT",
            "server": "Microsoft-IIS/10.0",
            "content-length": "195",
            "x-aspnet-version": "4.0.30319",
            "x-powered-by": "ASP.NET",
            "connection": "close",
            "content-type": "text/html; charset=utf-8"
        },
        "body": """<!doctype html>
<html lang="en" class="notranslate" translate="no">
<head>
<title>dklskj</title>
<meta charset="utf-8">
<meta name="google" content="notranslate">
<meta name="ip" content="**************">
<meta name="region" content="CN">
<link rel="icon" type="image/x-icon" href="/favicon.svg">
<link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600&family=Inter:wght@400;500&display=swap" rel="stylesheet" media="print" onload='this.media="all"'>
<script>
    !function(n, t) {
        if (n.LogAnalyticsObject = t,
        !n[t]) {
            var c = function() {
                c.q.push(arguments)
            };
            c.q = c.q || [],
            n[t] = c
        }
        n[t].l = +new Date
    }(window, "collectEvent")
</script>
<script async src="https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-rangers-v5.2.1.js"></script>
<script defer src="/main.js"></script>
<link href="/main.css" rel="stylesheet">
</head>
<body>
<div id="root"></div>
</body>
</html>""",
        "setCookies": {},
        "httpVersion": "1.1"
    }
}},
"https://www.zhuangku.com/.git/": {"扫描样例2": {
    "req": {
        "body": "",
        "remoteAddr": "",
        "url": "https://www.zhuangku.com/.git/",
        "header": {
            "content-length": "95",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "accept": "application/json, text/javascript, */*; q=0.01",
            "host": "www.zhuangku.com",
            "x-requested-with": "XMLHttpRequest",
            "content-type": "application/json",
            "connection": "close",
            "cache-control": "no-cache",
            "accept-encoding": "gzip, deflate",
            "user-agent": "python test"
        },
        "method": "POST",
        "httpVersion": "1.1"
    },
    "rsp": {
        "status": "200",
        "header": {
            "date": "Wed, 18 Aug 2021 02:55:45 GMT",
            "server": "Microsoft-IIS/10.0",
            "content-length": "195",
            "x-aspnet-version": "4.0.30319",
            "x-powered-by": "ASP.NET",
            "connection": "close",
            "content-type": "text/html; charset=utf-8"
        },
        "body": """<!doctype html>
<html lang="en" class="notranslate" translate="no">
<head>
<title>dklskj</title>
<meta charset="utf-8">
<meta name="google" content="notranslate">
<meta name="ip" content="**************">
<meta name="region" content="CN">
<link rel="icon" type="image/x-icon" href="/favicon.svg">
<link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600&family=Inter:wght@400;500&display=swap" rel="stylesheet" media="print" onload='this.media="all"'>
<script>
    !function(n, t) {
        if (n.LogAnalyticsObject = t,
        !n[t]) {
            var c = function() {
                c.q.push(arguments)
            };
            c.q = c.q || [],
            n[t] = c
        }
        n[t].l = +new Date
    }(window, "collectEvent")
</script>
<script async src="https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-rangers-v5.2.1.js"></script>
<script defer src="/main.js"></script>
<link href="/main.css" rel="stylesheet">
</head>
<body>
<div id="root"></div>
</body>
</html>""",
        "setCookies": {},
        "httpVersion": "1.1"
    }
}}}
result = p._extract_regex(samples)
p._save("www.yong.cn", result, list(samples.keys()))

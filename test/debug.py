import json
import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"

from openai import OpenAI
from web.prompt import SYSTEM_PROMPT
from tp_utils.log_util import TLogger
from tp_utils.nacos_util import NACOS_UTIL
from tp_utils.mongo_util import MONGO_UTIL

logger = TLogger.get_logger(os.path.basename(__file__))

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weakness_from_id",
            "description": "通过弱点ID获取弱点信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "weakness_id": {
                        "type": "string",
                        "description": "弱点ID",
                    }
                },
                "required": ["weakness_id"]
            },
        }
    },
]

class DebugTest:
    def __init__(self):
        self.system_prompt = [{
            "role": "system",
            "content": SYSTEM_PROMPT
        }]

        self.mongo = MONGO_UTIL

    def _init(self):
        self.httpApi = self.mongo.db.get_collection("httpApi")
        self.httpSample = self.mongo.db.get_collection("httpSample")
        self.LLMConfig = self.mongo.db.get_collection("LLMConfig")

        # 读取LLM配置
        llm_cfg = self.LLMConfig.find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            return False
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        self.client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        return True

    def send_messages(self, messages):
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            tools=tools
        )
        if hasattr(response.choices[0].message, "reasoning_content"):
            reasoning_content = response.choices[0].message.reasoning_content
            print(f"reasoning_content:{reasoning_content}")
        else:
            print("此模型不支持推理")
        return response.choices[0].message

    def run(self):
        if not self._init():
            logger.error("初始化失败")
            return

        messages = []
        messages = self.system_prompt + [{
            "role": "user",
            "content": "研判并处置弱点：W-250319-00014"
        }]

        message = self.send_messages(messages)
        print(f"Model>\t {message.content}")

        tool = message.tool_calls[0]
        messages.append({"role":"assistant","content":message.content})

        print(f"User>\t 执行工具{tool.function.name},参数为：{tool.function.arguments}")
        # messages.append({"role": "user", "content": f"执行工具{tool.function.name},参数为：{tool.function.arguments},工具返回结果：弱点未找到，请确认弱点ID是否正确"})
        # message = self.send_messages(messages)
        # print(f"Model>\t {message.content}")




def run():
    t = DebugTest()
    t.run()


if __name__ == '__main__':
    run()

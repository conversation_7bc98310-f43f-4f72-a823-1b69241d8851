import os
import tomllib
from pathlib import Path

# 获取当前文件所在目录
current_dir = Path(__file__).parent

# 根据环境变量选择配置文件
# if os.environ.get("PYTHON_ENV") == "dev":
#     config_path = current_dir / 'config_dev.toml'
# else:
#     config_path = current_dir / 'config.toml'
config_path = current_dir / 'config_dev.toml'

# 加载配置文件
with open(config_path, "rb") as f:
    config = tomllib.load(f)

# 导出配置对象
__all__ = ['config']

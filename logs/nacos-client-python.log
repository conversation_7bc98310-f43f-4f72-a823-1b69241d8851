2025-08-05 11:35:33,890 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 11:35:33,891 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:35:33,891 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:35:36,329 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 11:35:36,330 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:35:36,330 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:35:36,331 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 11:35:36,331 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.
2025-08-05 11:38:00,194 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 11:38:00,194 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:38:00,194 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:38:02,419 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 11:38:02,419 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:38:02,419 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:38:02,420 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 11:38:02,420 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.
2025-08-05 11:38:54,809 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 11:38:54,810 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:38:54,810 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:38:57,084 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 11:38:57,084 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 11:38:57,084 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 11:38:57,085 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 11:38:57,086 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.
2025-08-05 14:06:20,044 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 14:06:20,046 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:06:20,046 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:06:22,295 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 14:06:22,296 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:06:22,296 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:06:22,297 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 14:06:22,297 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.
2025-08-05 14:07:38,672 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 14:07:38,674 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:07:38,675 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:07:40,911 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 14:07:40,911 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:07:40,912 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:07:40,913 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 14:07:40,913 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.
2025-08-05 14:08:35,256 - WARNING - [do-sync-req] nacos-server:8848 request timeout
2025-08-05 14:08:35,257 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:08:35,257 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:08:37,483 - WARNING - [do-sync-req] server:nacos-server:8848 is not available for reason:Bad Gateway
2025-08-05 14:08:37,483 - ERROR - [do-sync-req] nacos-server:8848 maybe down, no server is currently available
2025-08-05 14:08:37,483 - ERROR - [get-config] exception All server are not available occur
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 470, in get_config
    resp = self._do_sync_req("/nacos/v1/cs/configs", None, params, None, timeout or self.default_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent/.venv/lib/python3.12/site-packages/nacos/client.py", line 742, in _do_sync_req
    raise NacosRequestException("All server are not available")
nacos.exception.NacosRequestException: All server are not available
2025-08-05 14:08:37,484 - INFO - [get-config] get config from server failed, try snapshot, data_id:discover.weakness.rules.json, group:discover, namespace:
2025-08-05 14:08:37,485 - INFO - [get-config] snapshot is not exist for discover.weakness.rules.json+discover+.

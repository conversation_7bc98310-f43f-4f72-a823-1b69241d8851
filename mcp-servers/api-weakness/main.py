import os

import datetime

from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))
from mcp.server.fastmcp import FastMCP
from typing import Literal
from pydantic import Field

from tp_utils.mongo_util import MONGO_UTIL
from tp_utils.nacos_util import NACOS_UTIL
from config import config

mcp = FastMCP("api-weakness")

WEAKNESS_MAP = NACOS_UTIL.get_weakness_desc()

WEAK_STATE_MAP = {
    "NEW": "待确认",
    "REPAIRING": "待修复",
    "IGNORED": "已忽略",
    "FIXED": "已修复",
    "REOPEN": "再复现"
}
REVERSE_WEAK_STATE_MAP = {v: k for k, v in WEAK_STATE_MAP.items()}


@mcp.tool(description="处置弱点")
async def handle_weakness_from_id(
    weakness_id: str = Field(description="弱点ID"),
    state_name: Literal["待修复", "已忽略"] = Field(description="处理状态, '待修复' 或者 '已忽略'"),
    reason: str = Field(description="处理依据")
) -> str:
    if state_name not in ["待修复", "已忽略"]:
        raise ValueError("state_name argument error")

    # 获取老弱点状态
    old_info = MONGO_UTIL.db.get_collection('httpApiWeakness').find_one({"operationId": weakness_id},
                                                                        {"_id": 1, "updateTime": 1, "state": 1})
    if not old_info:
        raise ValueError(f"Unknown weakness_id: {weakness_id}")

    # 更新弱点状态
    state = REVERSE_WEAK_STATE_MAP.get(state_name)
    MONGO_UTIL.db.get_collection("httpApiWeakness").update_one({"operationId": weakness_id},
                                                               {
                                                                   "$set": {
                                                                       "state": state,
                                                                       "aiInfo": {"weaknessOptReason": reason}
                                                                   }
                                                               })
    # 插入弱点状态更新日志
    MONGO_UTIL.db.get_collection('apiWeaknessLog').insert_one({
        "apiWeaknessId": str(old_info['_id']),
        "earlyTimestamp": old_info['updateTime'],
        "lastOperator": "AI",
        "state": state,
        "stateName": state_name,
        "updateTime": int(datetime.datetime.now().timestamp() * 1000),
        "oldState": old_info['state'],
        "oldStateName": WEAK_STATE_MAP.get(old_info['state'], "未知")
    })
    return "弱点处理成功"


@mcp.tool(description="通过弱点ID获取弱点信息")
async def get_weakness_from_id(
    weakness_id: str = Field(description="弱点ID"),
) -> str:
    data = MONGO_UTIL.db.get_collection('httpApiWeakness').find_one(
        {"operationId": weakness_id, "delFlag": False, "userDelFlag": False, "state": "NEW"})
    if not data:
        raise ValueError("未找到弱点")

    return assemble_info(data)


def assemble_info(data: dict) -> str:
    weakness_name = data.get("name")
    if not WEAKNESS_MAP.get(weakness_name):
        raise ValueError("弱点定义为空")

    samples = data.get("samples", [])
    if samples:
        sampleid = samples[0].get("sampleId", "")
    else:
        raise ValueError("Missing sample")

    sample = MONGO_UTIL.db.get_collection('httpSample').find_one({"_id": sampleid})
    req = sample.get("req", {})
    req = {
        "url": req.get("url"),
        "body": req.get("body", "")[:1000],
        "header": req.get("header"),
        "method": req.get("method")
    }
    rsp = sample.get("rsp", {})
    rsp = {
        "status": rsp.get("status"),
        "header": rsp.get("header"),
        "body": rsp.get("body", "")[:1000]
    }

    return (f"<弱点名称>{weakness_name}</弱点名称>\n"
            f"<弱点定义>{WEAKNESS_MAP[weakness_name]}</弱点定义>"
            f"<弱点ID>{data['operationId']}</弱点ID>"
            f"<http请求>{req}</http请求>"
            f"<http响应>{rsp}</http响应>")


if __name__ == "__main__":
    mcp.run(transport='stdio')
    # import asyncio
    # print(asyncio.run(get_weakness_from_id("37.10.167.186")))
    # asyncio.run(handle_risk_from_id("E-250313-00020", "已确认", "skdflsdkfjlksdjf"))

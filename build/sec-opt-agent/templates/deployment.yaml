---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: {{ .Values.metadata.name }}
  name: {{ .Values.metadata.name }}
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: {{ .Values.metadata.name }}
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: {{ .Values.metadata.name }}
    spec:
      containers:
        - env:
            {{ include "kubeEnv" . | nindent 12 }}
          image: '{{ .Values.image.registry}}{{.Values.image.name}}:{{.Values.image.tag}}'
          imagePullPolicy: IfNotPresent
          name: {{ .Values.metadata.name }}
{{/*          readinessProbe:*/}}
{{/*            failureThreshold: 3*/}}
{{/*            initialDelaySeconds: 10*/}}
{{/*            periodSeconds: 5*/}}
{{/*            successThreshold: 1*/}}
{{/*            tcpSocket:*/}}
{{/*              port: */}}
{{/*            timeoutSeconds: 5*/}}
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
{{/*          volumeMounts:*/}}
{{/*            - mountPath: {{ .Values.logPath }}*/}}
{{/*              name: dns-domain*/}}
{{/*              subPathExpr: $(KUBE_NS_NAME)/logs/$(KUBE_POD_GROUP_NAME)/$(KUBE_POD_NAME)*/}}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /opt/dsd/qzprod
            type: DirectoryOrCreate
          name: dns-domain

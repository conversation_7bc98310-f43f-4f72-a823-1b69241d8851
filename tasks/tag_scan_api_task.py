import json
import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"

from openai import OpenAI
from tp_utils.mongo_util import MONGO_UTIL
from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))

INTERVAL = 60 * 3 * 1  # fixme 为了测试方便改成3分钟跑一次


class ScanApi:
    def __init__(self):
        self.MAX_APIS = 1000  # 一个正常应用最多包含的接口数量，>=阈值说明可能存在扫描流量
        self.MIN_APIS = 2  # 一个正常应用最少包含的接口数量，<=阈值说明可能存在扫描流量
        self.MAX_API_VISIT = 1  # 接口的访问次数，<=阈值说明可能是扫描流量
        self.MIN_SAMPLES = 2  # >=2个扫描接口的样例，才能提取出扫描特征
        self.system_prompt = [{
            "role": "system",
            "content": f"""你是一个API日志分析助手，任务是：**判断一条 HTTP 请求/响应日志是否被渗透测试**，并输出最终的判断及理由。

---

## 判断规则
1. 根据URL、请求头、请求体、响应状态码、响应头、响应体等维度综合分析；
2. 除非有明确的渗透特征，否则不要判断为渗透测试。

---

## 输出格式
- 返回 JSON 对象，格式如下：
{{
  "aiTagScanReason": "用中文说明主要判断依据",
  "aiTagScan": True/False,
  "confidence":"你对自己做出的判断的信心"
}}
"""}]

        self.mongo = MONGO_UTIL

    def _init(self):
        self.httpApi = self.mongo.db.get_collection("httpApi")
        self.httpApp = self.mongo.db.get_collection("httpApp")
        self.httpSample = self.mongo.db.get_collection("httpSample")
        self.LLMConfig = self.mongo.db.get_collection("LLMConfig")

        # 读取LLM配置
        llm_cfg = self.LLMConfig.find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            return False
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        self.client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        return True

    def _get_uris(self, max_uris: int):
        """
        找出疑似扫描流量的接口，特征：
        1. 接口数量>1000的应用,接口数太多，很可能存在扫描流量接口
        2. 接口数量==1的应用，应用只有一个接口，可能是扫描流量
        3. 获取访问次数==1的接口，只被访问过一次，很可能是扫描流量接口
        :return:
        """
        hosts = []
        for app in self.httpApp.find(
            {
                "delFlag": False, "userDelFlag": False, "aiInfo.tagScan": {"$exists": False},
                "$or": [
                    {"appStat.apiCount": {"$gte": self.MAX_APIS}},
                    {"appStat.apiCount": {"$lte": self.MIN_APIS}},
                ]
            }):
            hosts.append(app['host'])

        for host in hosts:
            uris = []
            scan_uris_limit = 2  # 一个应用一次只挑选2个扫描流量接口
            for http_api in self.httpApi.find({
                "host": host,
                "apiStat.totalVisits": {"$lte": self.MAX_API_VISIT},  # 访问量只有1次的通常是扫描接口
                "aiInfo.tagScan": {"$exists": False},  # 没有被AI分析过的
                "delFlag": False,
                "userDelFlag": False,
            }, no_cursor_timeout=True):
                max_uris -= 1

                uris.append(http_api.get("uri", ""))
                scan_uris_limit -= 1
                if scan_uris_limit <= 0:
                    break
            # logger.debug(f"host:{host}, 当前扫描接口数量: {len(uris)}")
            # 筛选出来再yield，防止mongo连接超时
            for uri in uris:
                yield uri
            if max_uris <= 0:
                break

    def _get_sample(self, uri):
        http_sample = self.httpSample.find_one({"uri": uri}, {"req": 1, "rsp": 1})
        if http_sample:
            # 样例太长会让小模型胡言乱语，截断一下
            http_sample['rsp']['body'] = http_sample.get("rsp", {}).get("body", "")[:1000]
            http_sample['req']['body'] = http_sample.get("req", {}).get("body", "")[:1000]
            return {
                "req": http_sample.get("req"),
                "rsp": http_sample.get("rsp"),
            }

    def _analyze_api(self, sample) -> dict:
        messages = self.system_prompt + [{
            "role": "user",
            "content": str(sample)
        }]
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}
        )

        message = response.choices[0].message.content

        result = {}
        try:
            result = json.loads(message)
        except:
            logger.error("message is not a json")
        return result

    def _save_tag(self, uri, result: dict):
        api_info = self.httpApi.find_one({"uri": uri}, {"aiInfo": 1})
        aiInfo = api_info.get("aiInfo", {})
        buf = {
            "tagScanReason": result.get("aiTagScanReason", "error"),
            "tagScan": result.get("aiTagScan", False)
        }
        aiInfo.update(buf)
        self.httpApi.update_one({"uri": uri},
                                {"$set": {
                                    "aiInfo": aiInfo
                                }})

    def run(self, max_uris: int):
        if not self._init():
            logger.error("初始化失败")
            return

        uri_count = 0
        for uri in self._get_uris(max_uris):
            if uri_count % 100 == 0:
                logger.debug(f"uri_count: {uri_count}")
            uri_count += 1
            sample = self._get_sample(uri)
            if sample:
                result = self._analyze_api(sample)
                self._save_tag(uri, result)


def run(max_uris=4):  # fixme 为了测试方便改成一次最多研判4个接口
    s = ScanApi()
    s.run(max_uris)


if __name__ == '__main__':
    run(1)

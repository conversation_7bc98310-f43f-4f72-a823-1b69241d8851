import json
import os
import time

os.environ["OPENAI_API_KEY"] = "your-api-key-here"
import requests
from config import config
from openai import OpenAI
from tp_utils.mongo_util import MONGO_UTIL
from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))

INTERVAL = 60 * 10 * 1  # todo 为了测试方便临时改成10分钟跑一次


class ExtractScanRegex:
    def __init__(self):
        self.system_prompt = [{
            "role": "system",
            "content": f"""你是一个正则专家，任务是：根据用户提供的多个http扫描流量日志，**提取出正则表达式**，用于识别这类扫描流量。

---

## 判断规则
1. 从日志的**响应体**中提取扫描特征；
2. 提取的正则要**精简**，恰好能识别扫描流量，又不至于误识别正常流量。

---

## 输出格式
- 返回 JSON 对象，格式如下：
{{
  "aiScanRegex": "精简的正则",
  "aiScanRegexReason": "中文说明提取这个特征的理由"
}}
"""}]

        self.mongo = MONGO_UTIL

    def _init(self):
        self.httpApi = self.mongo.db.get_collection("httpApi")
        self.httpApp = self.mongo.db.get_collection("httpApp")
        self.httpSample = self.mongo.db.get_collection("httpSample")
        self.LLMConfig = self.mongo.db.get_collection("LLMConfig")
        self.smartFilterRule = self.mongo.db.get_collection("smartFilterRule")

        # 读取LLM配置
        llm_cfg = self.LLMConfig.find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            return False
        API_KEY = llm_cfg.get('apiKey')
        BASE_URL = llm_cfg.get('baseUrl')
        self.MODEL_NAME = llm_cfg.get('modelName')

        self.client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        return True

    def _get_hosts(self, max_hosts=1000):
        hosts = []

        black_hosts = []
        for black_host in self.httpApp.find({"aiInfo.aiScanRegex": {"$exists": True}}):
            black_hosts.append(black_host['host'])

        # 聚合查找：没有被AI提取过扫描特征 && 接口数量<=2 的应用
        pipeline = [
            {'$match': {'aiInfo.tagScan': True, "host": {"$nin": black_hosts}}},
            {'$group': {'_id': '$host', 'count': {'$sum': 1}}},
            {'$match': {'count': {'$gte': 2}}},
            {'$project': {'_id': 0, 'host': '$_id'}}
        ]

        result = self.httpApi.aggregate(pipeline, allowDiskUse=True)
        for doc in result:
            hosts.append(doc['host'])
            max_hosts -= 1
            if max_hosts <= 0:
                break

        for host in hosts:
            yield host

    def _get_sample(self, host: str):
        samples = {}
        uris = []
        max_uris = 2
        for http_api in self.httpApi.find(
            {
                "host": host,
                "aiInfo.tagScan": True,
                "delFlag": False,
                "userDelFlag": False
            }, {"uri": 1},
            no_cursor_timeout=True):
            uris.append(http_api.get("uri", ""))
            max_uris -= 1
            if max_uris <= 0:
                break

        for i, uri in enumerate(uris):
            http_sample = self.httpSample.find_one({"uri": uri})
            if http_sample:
                # 样例太长会让小模型胡言乱语，截断一下
                http_sample['rsp']['body'] = http_sample.get("rsp", {}).get("body", "")[:1000]
                http_sample['req']['body'] = http_sample.get("req", {}).get("body", "")[:1000]

                samples[uri] = {
                    f"扫描样例{i}": {
                        "请求": http_sample.get("req"),
                        "响应": http_sample.get("rsp"),
                    }
                }
        return samples

    def _extract_regex(self, samples: dict) -> dict:
        messages = self.system_prompt + [{
            "role": "user",
            "content": str(samples.values())
        }]
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}
        )

        message = response.choices[0].message.content
        return json.loads(message)

    def _save(self, host: str, result: dict, uris: list):
        aiScanRegex = result.get("aiScanRegex", "")
        aiScanRegexReason = result.get("aiScanRegexReason", "")
        self.httpApp.update_one({"host": host},
                                {"$set": {
                                    "aiInfo": {
                                        "aiScanRegex": aiScanRegex,
                                        "aiScanRegexReason": aiScanRegexReason
                                    }
                                }})
        # 添加扫描特征到过滤配置
        if aiScanRegex:
            now = int(time.time() * 1000)
            # 改为调用后端接口
            data = {
                "type": "SMART_FILTER_RULE",
                "smartFilterRuleAiInfo": {
                    "smartFilterRule": {
                        "host": host,
                        "effectScopes": [host],
                        "target": "RSP_CONTENT",
                        "enable": False,
                        "delFlag": False,
                        "createTime": now,
                        "updateTime": now,
                        "desc": aiScanRegexReason,
                        "resourceList": [aiScanRegex],
                        "ruleSource": "AI",
                        "matchApiUris": uris,
                        "matchApiCount": len(uris)
                    }
                }
            }
            headers = {
                "Content-Type": "application/json",
                "X-API-Key": "OPEN_API"
            }
            rsp = requests.post(f"http://{config.BACKEND_ADDR}/audit-apiv2/api/llmConfig/receiveAiStudyResult",
                                json=data,
                                headers=headers)
            if rsp.status_code != 200:
                logger.error(f"添加扫描特征到过滤配置失败: {rsp.text}")

    def run(self):
        if not self._init():
            logger.error("初始化失败")
            return

        host_count = 0
        for host in self._get_hosts():
            if host_count % 100 == 0:
                logger.debug(f"host_count: {host_count}")
            host_count += 1
            samples = self._get_sample(host)
            if samples:
                result = self._extract_regex(samples)
                self._save(host, result, list(samples.keys()))


def run():
    c = ExtractScanRegex()
    c.run()


if __name__ == '__main__':
    run()
